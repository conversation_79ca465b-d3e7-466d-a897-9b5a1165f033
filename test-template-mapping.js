// 简单测试货物类型到模板类型的映射函数
const { getTemplateTypeFromShipmentType } = require('./src/services/shippingFeeService');

// 测试数据
const testShipmentTypes = [
  { id: 1, code: 'GENERAL', name: '普通货物', isActive: true },
  { id: 2, code: 'BATTERY', name: '带电货物', isActive: true },
  { id: 3, code: 'POST_BOX', name: '投函货物', isActive: true },
  { id: 4, code: 'SPECIAL', name: '特殊货物', isActive: true },
];

console.log('测试货物类型到模板类型的映射：');
testShipmentTypes.forEach(type => {
  const templateType = getTemplateTypeFromShipmentType(type);
  console.log(`${type.name} (${type.code}) -> 模板类型 ${templateType}`);
});

// 预期结果：
// 普通货物 (GENERAL) -> 模板类型 1
// 带电货物 (BATTERY) -> 模板类型 2
// 投函货物 (POST_BOX) -> 模板类型 3
// 特殊货物 (SPECIAL) -> 模板类型 6
