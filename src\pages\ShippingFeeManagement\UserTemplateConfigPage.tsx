import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Select,
  Typography,
  Space,
  Button,
  message,
  Spin,
  Row,
  Col,
  Form,
  Input,
  List,
  Pagination,
  Avatar,
  Alert,
  Modal,
} from "antd";
import {
  SaveOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  getActiveShipmentTypes,
  getShippingFeeTemplatesByType,
  getUserTemplateConfigurations,
  updateUserTemplateConfigurations,
  getTemplateTypeFromShipmentType,
  ShipmentType,
  ShippingFeeTemplate,
  UserTemplateConfiguration,
} from "../../services/shippingFeeService";
import { fetchUserOptions } from "../../services/userService";
import CreateTemplateModal from "../../components/ShippingFeeTemplate/CreateTemplateModal";
import { useLocation } from "react-router-dom";
import styles from "./UserTemplateConfigPage.module.css";

const { Title, Text } = Typography;
const { Option } = Select;

// 用户选项接口
interface UserOption {
  id: number;
  username: string;
  nickname?: string;
  email?: string;
}

/**
 * 用户模板配置页面
 */
const UserTemplateConfigPage: React.FC = () => {
  // 状态管理
  const [users, setUsers] = useState<UserOption[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>(
    undefined
  );
  const [selectedUser, setSelectedUser] = useState<UserOption | undefined>(
    undefined
  );
  const [shipmentTypes, setShipmentTypes] = useState<ShipmentType[]>([]);
  const [templatesByType, setTemplatesByType] = useState<
    Record<number, ShippingFeeTemplate[]>
  >({});

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [userSearchKeyword, setUserSearchKeyword] = useState("");

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);

  // 表单实例
  const [form] = Form.useForm();

  // 新增模板相关状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedTypeForCreate, setSelectedTypeForCreate] = useState<
    number | undefined
  >(undefined);

  // 未保存更改检测相关状态
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<
    Record<string, number | undefined>
  >({});

  // React Router hooks
  const location = useLocation();

  // 用于存储当前表单值的引用
  const currentFormValuesRef = useRef<Record<string, number | undefined>>({});

  // 清空表单的辅助函数
  const clearFormFields = useCallback(() => {
    const emptyFormValues: Record<string, undefined> = {};
    shipmentTypes.forEach((type) => {
      emptyFormValues[`template_${type.id}`] = undefined;
    });
    form.setFieldsValue(emptyFormValues);
    setInitialFormValues(emptyFormValues);
    currentFormValuesRef.current = emptyFormValues;
  }, [shipmentTypes, form]);

  // 检测表单是否有变化
  const checkForChanges = useCallback(
    (currentValues: Record<string, number | undefined>) => {
      const hasChanges = Object.keys(currentValues).some((key) => {
        return currentValues[key] !== initialFormValues[key];
      });
      setHasUnsavedChanges(hasChanges);
      return hasChanges;
    },
    [initialFormValues]
  );

  // 处理表单值变化
  const handleFormValuesChange = useCallback(
    (
      changedValues: Record<string, number | undefined>,
      allValues: Record<string, number | undefined>
    ) => {
      currentFormValuesRef.current = allValues;

      // 只有在有初始值的情况下才检测变化
      if (Object.keys(initialFormValues).length > 0) {
        checkForChanges(allValues);
      }
    },
    [initialFormValues, checkForChanges]
  );

  // beforeunload 事件处理
  const handleBeforeUnload = useCallback(
    (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue =
          "您有未保存的配置更改，确定要离开吗？离开后更改将丢失。";
        return event.returnValue;
      }
    },
    [hasUnsavedChanges]
  );

  // 显示离开确认对话框
  const showLeaveConfirmation = useCallback(() => {
    return new Promise<boolean>((resolve) => {
      Modal.confirm({
        title: "未保存的更改",
        icon: <ExclamationCircleOutlined />,
        content: "您有未保存的配置更改，确定要离开吗？离开后更改将丢失。",
        okText: "确定离开",
        okType: "danger",
        cancelText: "取消",
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      });
    });
  }, []);

  // 获取用户列表
  const fetchUsers = async (keyword?: string, page = 1, size = 10) => {
    try {
      setUsersLoading(true);
      const response = await fetchUserOptions({
        keyword,
        page,
        pageSize: size,
      });
      setUsers(response.data?.list || []);
      setTotalUsers(response.data?.total || 0);
    } catch (error) {
      console.error("获取用户列表失败:", error);
      message.error("获取用户列表失败，请重试");
    } finally {
      setUsersLoading(false);
    }
  };

  // 获取货物类型列表
  const fetchShipmentTypes = async () => {
    try {
      const types = await getActiveShipmentTypes();
      setShipmentTypes(types);
    } catch (error) {
      console.error("获取货物类型失败:", error);
      message.error("获取货物类型失败，请重试");
    }
  };

  // 获取所有类型的运费模板
  const fetchAllTemplates = useCallback(async () => {
    try {
      const templatesMap: Record<number, ShippingFeeTemplate[]> = {};

      for (const type of shipmentTypes) {
        const response = await getShippingFeeTemplatesByType(type.id);
        templatesMap[type.id] = response.templates;
      }

      setTemplatesByType(templatesMap);
    } catch (error) {
      console.error("获取运费模板失败:", error);
      message.error("获取运费模板失败，请重试");
    }
  }, [shipmentTypes]);

  // 获取用户的模板配置
  const fetchUserConfigurations = useCallback(
    async (userId: number) => {
      try {
        setLoading(true);
        const response = await getUserTemplateConfigurations(userId);

        // 将用户已配置的模板转换为映射关系
        const configMap: Record<number, number> = {};
        response.templates.forEach((template) => {
          configMap[template.type] = template.id;
        });

        // 为所有货物类型设置表单值（确保清空未配置的字段）
        const formValues: Record<string, number | undefined> = {};
        shipmentTypes.forEach((type) => {
          const templateId = configMap[type.id];
          // 显式为每个字段设置值，有配置的设置具体值，没配置的设置为undefined
          formValues[`template_${type.id}`] = templateId || undefined;
        });

        form.setFieldsValue(formValues);
        // 设置初始值用于变化检测
        setInitialFormValues(formValues);
        currentFormValuesRef.current = formValues;
        // 清除未保存状态
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error("获取用户模板配置失败:", error);
        message.error("获取用户模板配置失败，请重试");

        // 出错时也要清空表单，避免显示错误的数据
        clearFormFields();
      } finally {
        setLoading(false);
      }
    },
    [shipmentTypes, form, clearFormFields]
  );

  // 处理用户选择
  const handleUserSelect = (user: UserOption) => {
    // 先清空表单，防止状态残留
    clearFormFields();

    setSelectedUserId(user.id);
    setSelectedUser(user);

    if (shipmentTypes.length > 0) {
      fetchUserConfigurations(user.id);
    }
  };

  // 处理用户搜索
  const handleUserSearch = () => {
    setCurrentPage(1); // 搜索时重置到第一页
    fetchUsers(userSearchKeyword, 1, pageSize);
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    setPageSize(newPageSize);
    fetchUsers(userSearchKeyword, page, newPageSize);
  };

  // 保存配置
  const handleSaveConfigurations = async () => {
    if (!selectedUserId) {
      message.warning("请先选择用户");
      return;
    }

    try {
      setSaving(true);

      // 获取表单值
      const formValues = await form.validateFields();

      // 构建配置数据
      const configurations: UserTemplateConfiguration[] = [];

      shipmentTypes.forEach((type) => {
        const templateId = formValues[`template_${type.id}`];
        if (templateId) {
          configurations.push({
            type: type.id,
            templateId: templateId,
          });
        }
      });

      await updateUserTemplateConfigurations(selectedUserId, {
        configurations,
      });

      message.success("用户模板配置保存成功");

      // 重新加载用户配置以确保数据同步
      await fetchUserConfigurations(selectedUserId);
    } catch (error) {
      console.error("保存用户模板配置失败:", error);
      message.error("保存用户模板配置失败，请重试");
    } finally {
      setSaving(false);
    }
  };

  // 重置配置
  const handleResetConfigurations = () => {
    if (selectedUserId) {
      // 先清空表单，然后重新加载配置
      clearFormFields();

      fetchUserConfigurations(selectedUserId);
    }
  };

  // 处理新增模板
  const handleCreateTemplate = (shipmentType: ShipmentType) => {
    const templateType = getTemplateTypeFromShipmentType(shipmentType);
    // 先设置模板类型，然后在下一个事件循环中打开弹窗，确保状态更新完成
    setSelectedTypeForCreate(templateType);
    // 使用 setTimeout 确保状态更新完成后再打开弹窗
    setTimeout(() => {
      setCreateModalVisible(true);
    }, 0);
  };

  // 处理取消创建模板
  const handleCancelCreateTemplate = () => {
    setCreateModalVisible(false);
    setSelectedTypeForCreate(undefined);
  };

  // 处理创建模板成功
  const handleCreateTemplateSuccess = async (template: ShippingFeeTemplate) => {
    setCreateModalVisible(false);
    setSelectedTypeForCreate(undefined);

    // 刷新对应货物类型的模板列表
    const shipmentType = shipmentTypes.find(
      (type) => getTemplateTypeFromShipmentType(type) === template.type
    );

    if (shipmentType) {
      try {
        const response = await getShippingFeeTemplatesByType(shipmentType.id);
        setTemplatesByType((prev) => ({
          ...prev,
          [shipmentType.id]: response.templates,
        }));

        // 自动选中新创建的模板
        form.setFieldValue(`template_${shipmentType.id}`, template.id);

        // 手动触发变化检测
        const currentValues = form.getFieldsValue();
        handleFormValuesChange({}, currentValues);

        message.success(`运费模板 "${template.name}" 创建成功并已自动选中`);
      } catch (error) {
        console.error("刷新模板列表失败:", error);
        message.error("模板创建成功，但刷新列表失败，请手动刷新页面");
      }
    }
  };

  // 绑定beforeunload事件和路由拦截
  useEffect(() => {
    // 绑定beforeunload事件
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [handleBeforeUnload]);

  // 路由拦截逻辑
  useEffect(() => {
    // 监听浏览器的返回/前进按钮
    const handlePopState = async (event: PopStateEvent) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        const shouldLeave = await showLeaveConfirmation();
        if (shouldLeave) {
          // 用户确认离开，清除状态并手动导航
          setHasUnsavedChanges(false);
          window.history.back();
        } else {
          // 用户取消离开，恢复历史状态
          window.history.pushState(null, "", location.pathname);
        }
      }
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [hasUnsavedChanges, showLeaveConfirmation, location.pathname]);

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchShipmentTypes();
  }, []);

  // 当货物类型加载完成后，获取所有模板
  useEffect(() => {
    if (shipmentTypes.length > 0) {
      fetchAllTemplates();
    }
  }, [shipmentTypes, fetchAllTemplates]);

  // 当用户和货物类型都加载完成后，如果有选中用户则加载其配置
  useEffect(() => {
    if (selectedUserId && shipmentTypes.length > 0) {
      fetchUserConfigurations(selectedUserId);
    }
  }, [selectedUserId, shipmentTypes, fetchUserConfigurations]);

  return (
    <div className={styles.container}>
      <Title level={4}>用户模板配置</Title>

      {/* 未保存更改提示 */}
      {hasUnsavedChanges && (
        <Alert
          message="您有未保存的配置更改"
          description="请记得及时保存您的配置更改，以免丢失数据。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={24}>
        {/* 客户选择区 */}
        <Col span={8}>
          <Card title="客户选择" className={styles.userSelectionCard}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Input.Search
                placeholder="搜索客户名称或邮箱"
                value={userSearchKeyword}
                onChange={(e) => setUserSearchKeyword(e.target.value)}
                onSearch={handleUserSearch}
                onPressEnter={handleUserSearch}
                loading={usersLoading}
                enterButton={<SearchOutlined />}
              />

              <Spin spinning={usersLoading}>
                <List
                  className={styles.userList}
                  dataSource={users}
                  renderItem={(user) => (
                    <List.Item
                      className={`${styles.userListItem} ${
                        selectedUserId === user.id
                          ? styles.selectedUserItem
                          : ""
                      }`}
                      onClick={() => handleUserSelect(user)}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={
                          <Space>
                            <Text strong>{user.username}</Text>
                            {user.nickname && (
                              <Text type="secondary">({user.nickname})</Text>
                            )}
                          </Space>
                        }
                        description={user.email}
                      />
                    </List.Item>
                  )}
                />
              </Spin>

              {/* 分页组件 */}
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalUsers}
                onChange={handlePageChange}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`
                }
                pageSizeOptions={["5", "10", "20", "50"]}
                size="small"
              />

              {selectedUser && (
                <Card size="small" className={styles.selectedUserCard}>
                  <Text strong>当前选中客户：</Text>
                  <br />
                  <Text>{selectedUser.username}</Text>
                  {selectedUser.nickname && (
                    <>
                      <br />
                      <Text type="secondary">{selectedUser.nickname}</Text>
                    </>
                  )}
                  {selectedUser.email && (
                    <>
                      <br />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {selectedUser.email}
                      </Text>
                    </>
                  )}
                </Card>
              )}
            </Space>
          </Card>
        </Col>

        {/* 模板配置区 */}
        <Col span={16}>
          <Card
            title="模板配置"
            className={styles.configCard}
            extra={
              selectedUserId && (
                <Space>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetConfigurations}
                    disabled={loading}
                  >
                    重置
                  </Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSaveConfigurations}
                    loading={saving}
                    disabled={loading}
                    className={hasUnsavedChanges ? styles.unsavedButton : ""}
                  >
                    保存配置
                    {hasUnsavedChanges && " *"}
                  </Button>
                </Space>
              )
            }
          >
            {!selectedUserId ? (
              <div className={styles.emptyState}>
                <Text type="secondary">请先选择客户</Text>
              </div>
            ) : (
              <Spin spinning={loading}>
                <Form
                  form={form}
                  layout="vertical"
                  onValuesChange={handleFormValuesChange}
                >
                  <Space
                    direction="vertical"
                    style={{ width: "100%" }}
                    size="large"
                  >
                    {shipmentTypes.map((type) => (
                      <Card
                        key={type.id}
                        size="small"
                        className={styles.typeCard}
                      >
                        <Row align="middle">
                          <Col span={6}>
                            <Text strong>{type.name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              类型代码: {type.code}
                            </Text>
                          </Col>
                          <Col span={18}>
                            <Row gutter={8} align="middle">
                              <Col flex="auto">
                                <Form.Item
                                  name={`template_${type.id}`}
                                  label="运费模板"
                                  style={{ marginBottom: 0 }}
                                >
                                  <Select
                                    placeholder="请选择运费模板"
                                    style={{ width: "100%" }}
                                    allowClear
                                    optionLabelProp="label"
                                  >
                                    {(templatesByType[type.id] || []).map(
                                      (template) => (
                                        <Option
                                          key={template.id}
                                          value={template.id}
                                          label={template.name}
                                        >
                                          <div
                                            className={styles.templateOption}
                                          >
                                            <div
                                              className={styles.templateName}
                                            >
                                              <Text strong>
                                                {template.name}
                                              </Text>
                                            </div>
                                            <div
                                              className={styles.templateDetails}
                                            >
                                              <Text
                                                type="secondary"
                                                style={{ fontSize: "11px" }}
                                              >
                                                首重: ¥
                                                {template.firstWeightPrice}/
                                                {template.firstWeightRange}kg |
                                                续重: ¥
                                                {template.continuedWeightPrice}/
                                                {
                                                  template.continuedWeightInterval
                                                }
                                                kg | 轻抛:{" "}
                                                {template.bulkCoefficient.toLocaleString()}{" "}
                                                | 三边:{" "}
                                                {template.threeSidesStart}cm
                                              </Text>
                                            </div>
                                          </div>
                                        </Option>
                                      )
                                    )}
                                  </Select>
                                </Form.Item>
                              </Col>
                              <Col>
                                <Button
                                  type="dashed"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleCreateTemplate(type)}
                                  style={{ marginTop: 30 }}
                                  title={`为${type.name}新增运费模板`}
                                >
                                  新增
                                </Button>
                              </Col>
                            </Row>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                  </Space>
                </Form>
              </Spin>
            )}
          </Card>
        </Col>
      </Row>

      {/* 新增模板弹窗 */}
      <CreateTemplateModal
        visible={createModalVisible}
        selectedTypeId={selectedTypeForCreate}
        onCancel={handleCancelCreateTemplate}
        onSuccess={handleCreateTemplateSuccess}
      />
    </div>
  );
};

export default UserTemplateConfigPage;
