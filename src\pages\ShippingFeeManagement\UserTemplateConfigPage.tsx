import React, { useState, useEffect } from "react";
import {
  Card,
  Select,
  Typography,
  Space,
  Button,
  message,
  Spin,
  Row,
  Col,
  Form,
  Input,
  List,
  Pagination,
  Avatar,
} from "antd";
import {
  SaveOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  getActiveShipmentTypes,
  getShippingFeeTemplatesByType,
  getUserTemplateConfigurations,
  updateUserTemplateConfigurations,
  ShipmentType,
  ShippingFeeTemplate,
  UserTemplateConfiguration,
} from "../../services/shippingFeeService";
import { fetchUserOptions } from "../../services/userService";
import styles from "./UserTemplateConfigPage.module.css";

const { Title, Text } = Typography;
const { Option } = Select;

// 用户选项接口
interface UserOption {
  id: number;
  username: string;
  nickname?: string;
  email?: string;
}

/**
 * 用户模板配置页面
 */
const UserTemplateConfigPage: React.FC = () => {
  // 状态管理
  const [users, setUsers] = useState<UserOption[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>(
    undefined
  );
  const [selectedUser, setSelectedUser] = useState<UserOption | undefined>(
    undefined
  );
  const [shipmentTypes, setShipmentTypes] = useState<ShipmentType[]>([]);
  const [templatesByType, setTemplatesByType] = useState<
    Record<number, ShippingFeeTemplate[]>
  >({});

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [userSearchKeyword, setUserSearchKeyword] = useState("");

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);

  // 表单实例
  const [form] = Form.useForm();

  // 获取用户列表
  const fetchUsers = async (keyword?: string, page = 1, size = 10) => {
    try {
      setUsersLoading(true);
      const response = await fetchUserOptions({
        keyword,
        page,
        pageSize: size,
      });
      setUsers(response.data?.list || []);
      setTotalUsers(response.data?.total || 0);
    } catch (error) {
      console.error("获取用户列表失败:", error);
      message.error("获取用户列表失败，请重试");
    } finally {
      setUsersLoading(false);
    }
  };

  // 获取货物类型列表
  const fetchShipmentTypes = async () => {
    try {
      const types = await getActiveShipmentTypes();
      setShipmentTypes(types);
    } catch (error) {
      console.error("获取货物类型失败:", error);
      message.error("获取货物类型失败，请重试");
    }
  };

  // 获取所有类型的运费模板
  const fetchAllTemplates = async () => {
    try {
      const templatesMap: Record<number, ShippingFeeTemplate[]> = {};

      for (const type of shipmentTypes) {
        const response = await getShippingFeeTemplatesByType(type.id);
        templatesMap[type.id] = response.templates;
      }

      setTemplatesByType(templatesMap);
    } catch (error) {
      console.error("获取运费模板失败:", error);
      message.error("获取运费模板失败，请重试");
    }
  };

  // 获取用户的模板配置
  const fetchUserConfigurations = async (userId: number) => {
    try {
      setLoading(true);
      const response = await getUserTemplateConfigurations(userId);

      // 将用户已配置的模板转换为映射关系
      const configMap: Record<number, number> = {};
      response.templates.forEach((template) => {
        configMap[template.type] = template.id;
      });

      // 设置表单值
      const formValues: Record<string, number> = {};
      shipmentTypes.forEach((type) => {
        const templateId = configMap[type.id];
        if (templateId) {
          formValues[`template_${type.id}`] = templateId;
        }
      });
      form.setFieldsValue(formValues);
    } catch (error) {
      console.error("获取用户模板配置失败:", error);
      message.error("获取用户模板配置失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 处理用户选择
  const handleUserSelect = (user: UserOption) => {
    setSelectedUserId(user.id);
    setSelectedUser(user);

    if (shipmentTypes.length > 0) {
      fetchUserConfigurations(user.id);
    }
  };

  // 处理用户搜索
  const handleUserSearch = () => {
    setCurrentPage(1); // 搜索时重置到第一页
    fetchUsers(userSearchKeyword, 1, pageSize);
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    setPageSize(newPageSize);
    fetchUsers(userSearchKeyword, page, newPageSize);
  };

  // 保存配置
  const handleSaveConfigurations = async () => {
    if (!selectedUserId) {
      message.warning("请先选择用户");
      return;
    }

    try {
      setSaving(true);

      // 获取表单值
      const formValues = await form.validateFields();

      // 构建配置数据
      const configurations: UserTemplateConfiguration[] = [];

      shipmentTypes.forEach((type) => {
        const templateId = formValues[`template_${type.id}`];
        if (templateId) {
          configurations.push({
            type: type.id,
            templateId: templateId,
          });
        }
      });

      await updateUserTemplateConfigurations(selectedUserId, {
        configurations,
      });

      message.success("用户模板配置保存成功");

      // 重新加载用户配置以确保数据同步
      fetchUserConfigurations(selectedUserId);
    } catch (error) {
      console.error("保存用户模板配置失败:", error);
      message.error("保存用户模板配置失败，请重试");
    } finally {
      setSaving(false);
    }
  };

  // 重置配置
  const handleResetConfigurations = () => {
    if (selectedUserId) {
      fetchUserConfigurations(selectedUserId);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchShipmentTypes();
  }, []);

  // 当货物类型加载完成后，获取所有模板
  useEffect(() => {
    if (shipmentTypes.length > 0) {
      fetchAllTemplates();
    }
  }, [shipmentTypes]);

  // 当用户和货物类型都加载完成后，如果有选中用户则加载其配置
  useEffect(() => {
    if (selectedUserId && shipmentTypes.length > 0) {
      fetchUserConfigurations(selectedUserId);
    }
  }, [selectedUserId, shipmentTypes]);

  return (
    <div className={styles.container}>
      <Title level={4}>用户模板配置</Title>

      <Row gutter={24}>
        {/* 客户选择区 */}
        <Col span={8}>
          <Card title="客户选择" className={styles.userSelectionCard}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Input.Search
                placeholder="搜索客户名称或邮箱"
                value={userSearchKeyword}
                onChange={(e) => setUserSearchKeyword(e.target.value)}
                onSearch={handleUserSearch}
                onPressEnter={handleUserSearch}
                loading={usersLoading}
                enterButton={<SearchOutlined />}
              />

              <Spin spinning={usersLoading}>
                <List
                  className={styles.userList}
                  dataSource={users}
                  renderItem={(user) => (
                    <List.Item
                      className={`${styles.userListItem} ${
                        selectedUserId === user.id
                          ? styles.selectedUserItem
                          : ""
                      }`}
                      onClick={() => handleUserSelect(user)}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={
                          <Space>
                            <Text strong>{user.username}</Text>
                            {user.nickname && (
                              <Text type="secondary">({user.nickname})</Text>
                            )}
                          </Space>
                        }
                        description={user.email}
                      />
                    </List.Item>
                  )}
                />
              </Spin>

              {/* 分页组件 */}
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalUsers}
                onChange={handlePageChange}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`
                }
                pageSizeOptions={["5", "10", "20", "50"]}
                size="small"
              />

              {selectedUser && (
                <Card size="small" className={styles.selectedUserCard}>
                  <Text strong>当前选中客户：</Text>
                  <br />
                  <Text>{selectedUser.username}</Text>
                  {selectedUser.nickname && (
                    <>
                      <br />
                      <Text type="secondary">{selectedUser.nickname}</Text>
                    </>
                  )}
                  {selectedUser.email && (
                    <>
                      <br />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {selectedUser.email}
                      </Text>
                    </>
                  )}
                </Card>
              )}
            </Space>
          </Card>
        </Col>

        {/* 模板配置区 */}
        <Col span={16}>
          <Card
            title="模板配置"
            className={styles.configCard}
            extra={
              selectedUserId && (
                <Space>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetConfigurations}
                    disabled={loading}
                  >
                    重置
                  </Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSaveConfigurations}
                    loading={saving}
                    disabled={loading}
                  >
                    保存配置
                  </Button>
                </Space>
              )
            }
          >
            {!selectedUserId ? (
              <div className={styles.emptyState}>
                <Text type="secondary">请先选择客户</Text>
              </div>
            ) : (
              <Spin spinning={loading}>
                <Form form={form} layout="vertical">
                  <Space
                    direction="vertical"
                    style={{ width: "100%" }}
                    size="large"
                  >
                    {shipmentTypes.map((type) => (
                      <Card
                        key={type.id}
                        size="small"
                        className={styles.typeCard}
                      >
                        <Row align="middle">
                          <Col span={6}>
                            <Text strong>{type.name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              类型代码: {type.code}
                            </Text>
                          </Col>
                          <Col span={18}>
                            <Form.Item
                              name={`template_${type.id}`}
                              label="运费模板"
                              style={{ marginBottom: 0 }}
                            >
                              <Select
                                placeholder="请选择运费模板"
                                style={{ width: "100%" }}
                                allowClear
                                optionLabelProp="label"
                              >
                                {(templatesByType[type.id] || []).map(
                                  (template) => (
                                    <Option
                                      key={template.id}
                                      value={template.id}
                                      label={template.name}
                                    >
                                      <div className={styles.templateOption}>
                                        <div className={styles.templateName}>
                                          <Text strong>{template.name}</Text>
                                        </div>
                                        <div className={styles.templateDetails}>
                                          <Text
                                            type="secondary"
                                            style={{ fontSize: "11px" }}
                                          >
                                            首重: ¥{template.firstWeightPrice}/
                                            {template.firstWeightRange}kg |
                                            续重: ¥
                                            {template.continuedWeightPrice}/
                                            {template.continuedWeightInterval}kg
                                            | 轻抛:{" "}
                                            {template.bulkCoefficient.toLocaleString()}{" "}
                                            | 三边: {template.threeSidesStart}cm
                                          </Text>
                                        </div>
                                      </div>
                                    </Option>
                                  )
                                )}
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                  </Space>
                </Form>
              </Spin>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default UserTemplateConfigPage;
