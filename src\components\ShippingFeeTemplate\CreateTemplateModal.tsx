import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Space,
  Typography,
} from "antd";
import {
  createShippingFeeTemplate,
  updateShippingFeeTemplate,
  CreateShippingFeeTemplateRequest,
  UpdateShippingFeeTemplateRequest,
  ShippingFeeTemplate,
} from "../../services/shippingFeeService";
import styles from "./CreateTemplateModal.module.css";

const { Text } = Typography;
const { Option } = Select;

interface CreateTemplateModalProps {
  visible: boolean;
  selectedTypeId?: number;
  editingTemplate?: ShippingFeeTemplate; // 编辑时传入的模板数据
  onCancel: () => void;
  onSuccess: (template: ShippingFeeTemplate) => void;
}

/**
 * 新增运费模板表单Modal
 */
const CreateTemplateModal: React.FC<CreateTemplateModalProps> = ({
  visible,
  selectedTypeId,
  editingTemplate,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 判断是否为编辑模式
  const isEditing = !!editingTemplate;

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEditing && editingTemplate) {
        // 编辑模式
        const updateData: UpdateShippingFeeTemplateRequest = {
          id: editingTemplate.id,
          name: values.name,
          firstWeightPrice: values.firstWeightPrice,
          firstWeightRange: values.firstWeightRange,
          continuedWeightPrice: values.continuedWeightPrice,
          continuedWeightInterval: values.continuedWeightInterval,
          bulkCoefficient: values.bulkCoefficient,
          threeSidesStart: values.threeSidesStart,
          type: values.type,
        };

        const updatedTemplate = await updateShippingFeeTemplate(updateData);
        message.success("运费模板更新成功");
        onSuccess(updatedTemplate);
      } else {
        // 创建模式
        const templateData: CreateShippingFeeTemplateRequest = {
          name: values.name,
          firstWeightPrice: values.firstWeightPrice,
          firstWeightRange: values.firstWeightRange,
          continuedWeightPrice: values.continuedWeightPrice,
          continuedWeightInterval: values.continuedWeightInterval,
          bulkCoefficient: values.bulkCoefficient,
          threeSidesStart: values.threeSidesStart,
          type: values.type,
        };

        const newTemplate = await createShippingFeeTemplate(templateData);
        message.success("运费模板创建成功");
        onSuccess(newTemplate);
      }

      form.resetFields();
    } catch (error) {
      console.error(
        isEditing ? "更新运费模板失败:" : "创建运费模板失败:",
        error
      );
      // API错误会由拦截器处理，这里不需要额外处理
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 模板类型选项
  const templateTypeOptions = [
    { value: 1, label: "普通模板" },
    { value: 2, label: "带电模板" },
    { value: 3, label: "投函模板" },
    { value: 6, label: "特殊模板" },
  ];

  // 设置表单初始值
  const getInitialValues = () => {
    if (isEditing && editingTemplate) {
      return {
        name: editingTemplate.name,
        type: editingTemplate.type,
        firstWeightPrice: editingTemplate.firstWeightPrice,
        firstWeightRange: editingTemplate.firstWeightRange,
        continuedWeightPrice: editingTemplate.continuedWeightPrice,
        continuedWeightInterval: editingTemplate.continuedWeightInterval,
        bulkCoefficient: editingTemplate.bulkCoefficient,
        threeSidesStart: editingTemplate.threeSidesStart,
      };
    }
    return {
      type: selectedTypeId,
      firstWeightRange: 0.5,
      continuedWeightInterval: 0.5,
      bulkCoefficient: 6000,
      threeSidesStart: 100,
    };
  };

  return (
    <Modal
      title={isEditing ? "编辑运费模板" : "新增运费模板"}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnClose
      className={styles.modal}
    >
      <Form
        form={form}
        layout="vertical"
        className={styles.form}
        initialValues={getInitialValues()}
      >
        <Form.Item
          label="模板名称"
          name="name"
          rules={[
            { required: true, message: "请输入模板名称" },
            { max: 100, message: "模板名称不能超过100个字符" },
          ]}
        >
          <Input placeholder="请输入运费模板名称" />
        </Form.Item>

        <Form.Item
          label="模板类型"
          name="type"
          rules={[{ required: true, message: "请选择模板类型" }]}
        >
          <Select placeholder="请选择模板类型">
            {templateTypeOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Space direction="vertical" style={{ width: "100%" }}>
          <Text strong className={styles.sectionTitle}>
            重量定价规则
          </Text>
          <div className={styles.formRow}>
            <Form.Item
              label="首重价格 (元)"
              name="firstWeightPrice"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入首重价格" },
                { type: "number", min: 0, message: "价格不能为负数" },
              ]}
            >
              <InputNumber
                placeholder="0.00"
                min={0}
                step={0.1}
                precision={2}
                className={styles.inputNumber}
              />
            </Form.Item>

            <Form.Item
              label="首重范围 (kg)"
              name="firstWeightRange"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入首重范围" },
                { type: "number", min: 0.1, message: "首重范围不能小于0.1kg" },
              ]}
            >
              <InputNumber
                placeholder="0.5"
                min={0.1}
                step={0.1}
                precision={1}
                className={styles.inputNumber}
              />
            </Form.Item>
          </div>

          <div className={styles.formRow}>
            <Form.Item
              label="续重价格 (元)"
              name="continuedWeightPrice"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入续重价格" },
                { type: "number", min: 0, message: "价格不能为负数" },
              ]}
            >
              <InputNumber
                placeholder="0.00"
                min={0}
                step={0.1}
                precision={2}
                className={styles.inputNumber}
              />
            </Form.Item>

            <Form.Item
              label="续重间隔 (kg)"
              name="continuedWeightInterval"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入续重间隔" },
                { type: "number", min: 0.1, message: "续重间隔不能小于0.1kg" },
              ]}
            >
              <InputNumber
                placeholder="0.5"
                min={0.1}
                step={0.1}
                precision={1}
                className={styles.inputNumber}
              />
            </Form.Item>
          </div>
        </Space>

        <Space direction="vertical" style={{ width: "100%" }}>
          <Text strong className={styles.sectionTitle}>
            体积计费规则
          </Text>
          <div className={styles.formRow}>
            <Form.Item
              label="轻抛系数"
              name="bulkCoefficient"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入轻抛系数" },
                { type: "number", min: 1000, message: "轻抛系数不能小于1000" },
              ]}
              tooltip="用于计算体积重量，常见值为5000或6000"
            >
              <InputNumber
                placeholder="5000"
                min={1000}
                step={100}
                className={styles.inputNumber}
              />
            </Form.Item>

            <Form.Item
              label="三边和阈值 (cm)"
              name="threeSidesStart"
              className={styles.formColumn}
              rules={[
                { required: true, message: "请输入三边和阈值" },
                { type: "number", min: 0, message: "阈值不能为负数" },
              ]}
              tooltip="超过此数值将按体积计费"
            >
              <InputNumber
                placeholder="60.0"
                min={0}
                step={1}
                precision={1}
                className={styles.inputNumber}
              />
            </Form.Item>
          </div>
        </Space>
      </Form>
    </Modal>
  );
};

export default CreateTemplateModal;
